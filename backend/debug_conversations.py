#!/usr/bin/env python3
"""
对话接口调试脚本

用于诊断 /api/chat/conversations 接口的 500 错误问题。
这个脚本会模拟接口调用，帮助定位具体的错误原因。

使用方法:
    python debug_conversations.py

作者: jinglv
时间: 2025年1月
"""

import asyncio
import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from config import TORTOISE_ORM
from models.chat import Conversation, Message, MessageRole, MessageStatus
from schemas.chat import ConversationResponse


async def test_database_connection():
    """测试数据库连接"""
    print("=" * 60)
    print("测试数据库连接")
    print("=" * 60)
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        print("✅ 数据库连接成功")
        
        # 检查表是否存在
        connection = Tortoise.get_connection("default")
        
        # 检查 conversations 表
        result = await connection.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='conversations'"
        )
        if result[1]:
            print("✅ conversations 表存在")
        else:
            print("❌ conversations 表不存在")
            return False
            
        # 检查 messages 表
        result = await connection.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='messages'"
        )
        if result[1]:
            print("✅ messages 表存在")
        else:
            print("❌ messages 表不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False


async def test_conversation_model():
    """测试 Conversation 模型操作"""
    print("\n" + "=" * 60)
    print("测试 Conversation 模型操作")
    print("=" * 60)
    
    try:
        # 测试创建对话
        print("🔄 测试创建对话...")
        conversation = await Conversation.create(title="测试对话")
        print(f"✅ 创建对话成功: ID={conversation.id}, 标题={conversation.title}")
        
        # 测试查询所有对话
        print("\n🔄 测试查询所有对话...")
        conversations = await Conversation.all()
        print(f"✅ 查询成功，共 {len(conversations)} 个对话")
        
        # 测试预加载消息
        print("\n🔄 测试预加载消息...")
        conversations_with_messages = await Conversation.all().prefetch_related("messages")
        print(f"✅ 预加载成功，共 {len(conversations_with_messages)} 个对话")
        
        for conv in conversations_with_messages:
            print(f"  - 对话 {conv.id}: {conv.title} (消息数: {len(conv.messages)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversation 模型操作失败: {e}")
        traceback.print_exc()
        return False


async def test_conversation_response_schema():
    """测试 ConversationResponse schema"""
    print("\n" + "=" * 60)
    print("测试 ConversationResponse Schema")
    print("=" * 60)
    
    try:
        # 获取一个对话
        conversations = await Conversation.all().prefetch_related("messages")
        if not conversations:
            print("⚠️ 没有对话数据，创建一个测试对话")
            conversation = await Conversation.create(title="Schema测试对话")
            conversations = [conversation]
        
        conv = conversations[0]
        
        # 测试创建 ConversationResponse
        print("🔄 测试创建 ConversationResponse...")
        response = ConversationResponse(
            id=conv.id,
            title=conv.title,
            created_at=conv.created_at.isoformat(),
            updated_at=conv.updated_at.isoformat(),
            message_count=len(conv.messages) if hasattr(conv, 'messages') else 0
        )
        
        print(f"✅ ConversationResponse 创建成功:")
        print(f"  - ID: {response.id}")
        print(f"  - 标题: {response.title}")
        print(f"  - 创建时间: {response.created_at}")
        print(f"  - 更新时间: {response.updated_at}")
        print(f"  - 消息数量: {response.message_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConversationResponse Schema 测试失败: {e}")
        traceback.print_exc()
        return False


async def test_get_conversations_logic():
    """测试获取对话列表的完整逻辑"""
    print("\n" + "=" * 60)
    print("测试获取对话列表的完整逻辑")
    print("=" * 60)
    
    try:
        # 模拟 API 接口的逻辑
        print("🔄 执行 API 接口逻辑...")
        
        # 获取所有对话并预加载相关消息
        conversations = await Conversation.all().prefetch_related("messages")
        print(f"✅ 获取对话成功，共 {len(conversations)} 个对话")
        
        # 构建响应列表
        response_list = []
        for conv in conversations:
            response = ConversationResponse(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=len(conv.messages)  # 计算消息数量
            )
            response_list.append(response)
        
        print(f"✅ 构建响应列表成功，共 {len(response_list)} 个响应")
        
        # 打印详细信息
        for i, response in enumerate(response_list):
            print(f"  {i+1}. ID={response.id}, 标题='{response.title}', 消息数={response.message_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取对话列表逻辑测试失败: {e}")
        traceback.print_exc()
        return False


async def create_test_data():
    """创建一些测试数据"""
    print("\n" + "=" * 60)
    print("创建测试数据")
    print("=" * 60)
    
    try:
        # 创建几个测试对话
        conversations_data = [
            "测试对话1",
            "测试对话2", 
            "文件上传测试对话"
        ]
        
        for title in conversations_data:
            # 检查是否已存在
            existing = await Conversation.filter(title=title).first()
            if not existing:
                conversation = await Conversation.create(title=title)
                print(f"✅ 创建对话: {title} (ID: {conversation.id})")
                
                # 为每个对话创建一些测试消息
                await Message.create(
                    conversation=conversation,
                    role=MessageRole.USER,
                    content=f"这是 {title} 的测试消息",
                    status=MessageStatus.COMPLETED
                )
                
                await Message.create(
                    conversation=conversation,
                    role=MessageRole.ASSISTANT,
                    content=f"这是对 {title} 的回复",
                    status=MessageStatus.COMPLETED
                )
                
                print(f"  - 为对话 {title} 创建了 2 条消息")
            else:
                print(f"⚠️ 对话 {title} 已存在，跳过创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("对话接口调试脚本")
    print("用于诊断 /api/chat/conversations 接口的 500 错误")
    
    try:
        # 1. 测试数据库连接
        if not await test_database_connection():
            print("\n❌ 数据库连接测试失败，请先解决数据库问题")
            return
        
        # 2. 创建测试数据
        await create_test_data()
        
        # 3. 测试 Conversation 模型操作
        if not await test_conversation_model():
            print("\n❌ Conversation 模型测试失败")
            return
        
        # 4. 测试 ConversationResponse schema
        if not await test_conversation_response_schema():
            print("\n❌ ConversationResponse Schema 测试失败")
            return
        
        # 5. 测试完整的获取对话列表逻辑
        if not await test_get_conversations_logic():
            print("\n❌ 获取对话列表逻辑测试失败")
            return
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！")
        print("如果 API 接口仍然报错，可能是以下原因：")
        print("1. FastAPI 应用初始化问题")
        print("2. 路由注册问题")
        print("3. 中间件或依赖注入问题")
        print("4. 请求处理过程中的其他错误")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生未预期的错误: {e}")
        traceback.print_exc()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("\n🔚 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
