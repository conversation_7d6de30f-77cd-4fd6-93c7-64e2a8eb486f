import asyncio
import re
import time
from typing import AsyncGenerator, Dict, List, Optional, Any

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import settings
from models import Conversation, Message, MessageRole, MessageStatus


class AIService:
    """AI服务类，负责处理与Autogen的交互"""

    def __init__(self):
        self.agents: Dict[str, AssistantAgent] = {}
        self.group_chats: Dict[int, RoundRobinGroupChat] = {}
        self.openai_client = None
        self._initialize_agents()

    def _initialize_agents(self):
        """初始化AI Agents"""
        try:
            # 创建模型客户端
            model_clients = []

            if settings.openai_api_key:
                openai_client = OpenAIChatCompletionClient(
                    model=settings.default_model,
                    api_key=settings.openai_api_key,
                    base_url=settings.openai_base_url,
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": ModelFamily.UNKNOWN,
                        "structured_output": True,
                        "multiple_system_messages": True,
                    }
                )
                model_clients.append(openai_client)

            if not model_clients:
                # 如果没有配置API密钥，使用默认配置
                print("警告: 未配置AI模型API密钥，将使用模拟模式")
                return

            # 创建助手Agent
            self.agents["assistant"] = AssistantAgent(
                name="assistant",
                model_client=model_clients[0],
                system_message="你是一个有用的AI助手，能够回答各种问题并提供帮助。请用中文回复。"
            )

            print(f"✅ Autogen Agent初始化成功，使用模型: {settings.default_model}")
        except Exception as e:
            print(f"初始化AI Agents失败: {e}")

    async def create_conversation(self, title: str = "新对话") -> Conversation:
        """创建新对话"""
        conversation = await Conversation.create(title=title)
        return conversation

    async def get_conversation(self, conversation_id: int) -> Optional[Conversation]:
        """获取对话"""
        return await Conversation.get_or_none(id=conversation_id)

    async def get_conversation_messages(self, conversation_id: int) -> List[Message]:
        """获取对话消息历史"""
        return await Message.filter(conversation_id=conversation_id).order_by("created_at")

    async def send_message(
            self,
            conversation_id: int,
            content: str,
            role: MessageRole = MessageRole.USER
    ) -> Message:
        """发送消息"""
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 保存用户消息
        user_message = await Message.create(
            conversation=conversation,
            role=role,
            content=content,
            status=MessageStatus.COMPLETED
        )
        return user_message

    async def _auto_set_conversation_title(self, conversation: Conversation, user_message: str):
        """根据第一条用户消息自动设置对话标题"""
        # 检查是否是第一条用户消息（除了刚创建的消息，应该只有1条消息）
        message_count = await Message.filter(conversation=conversation).count()

        if message_count <= 1 and conversation.title == "新对话":
            # 生成简洁的标题（取前30个字符）
            title = user_message.strip()
            if len(title) > 30:
                title = title[:30] + "..."

            # 移除换行符和多余空格
            title = " ".join(title.split())

            # 更新对话标题
            conversation.title = title
            await conversation.save()
            print(f"✅ 自动设置对话标题: {title}")

    def _estimate_tokens(self, input_text: str, output_text: str) -> int:
        """估算Token使用量（简单估算方法）"""
        # 简单的Token估算：
        # 1. 英文：约4个字符 = 1个token
        # 2. 中文：约1.5个字符 = 1个token
        # 3. 代码：约3个字符 = 1个token

        def count_tokens_for_text(text: str) -> int:
            if not text:
                return 0

            # 统计中文字符
            chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
            # 统计英文和其他字符
            other_chars = len(text) - chinese_chars

            # 检查是否包含代码块
            if '```' in text or 'def ' in text or 'function ' in text:
                # 代码内容，使用更精确的估算
                return int(chinese_chars * 0.7 + other_chars * 0.3)
            else:
                # 普通文本
                return int(chinese_chars * 0.7 + other_chars * 0.25)

        input_tokens = count_tokens_for_text(input_text)
        output_tokens = count_tokens_for_text(output_text)

        total_tokens = input_tokens + output_tokens
        print(f"📊 Token估算: 输入={input_tokens}, 输出={output_tokens}, 总计={total_tokens}")

        return total_tokens

    async def generate_response_stream(
            self,
            conversation_id: int,
            user_message: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成流式响应"""
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")
        # 保存用户消息
        await self.send_message(conversation_id, user_message, MessageRole.USER)

        # 检查是否需要自动设置对话标题
        await self._auto_set_conversation_title(conversation, user_message)

        # 创建助手消息记录
        assistant_message = await Message.create(
            conversation=conversation,
            role=MessageRole.ASSISTANT,
            content="",
            status=MessageStatus.PROCESSING
        )
        start_time = time.time()
        try:
            # 获取消息历史
            messages = await self.get_conversation_messages(conversation_id)
            # 构建对话历史
            chat_history = []
            for msg in messages[:-1]:  # 排除刚创建的assistant_message
                if msg.role != MessageRole.SYSTEM:
                    chat_history.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })
            # 添加当前用户消息
            chat_history.append({
                "role": "user",
                "content": user_message
            })
            response_content = ""
            # 尝试使用Autogen
            if self.agents:
                try:
                    agent = self.agents["assistant"]
                    user_msg = TextMessage(content=user_message, source="user")
                    response = await agent.on_messages([user_msg], cancellation_token=None)

                    if hasattr(response, 'chat_message') and response.chat_message:
                        full_response = response.chat_message.content

                        # 模拟流式输出 - 智能分割，保护代码块
                        def smart_split_content(content: str) -> list:
                            chunks = []
                            current_chunk = ""
                            in_code_block = False
                            in_inline_code = False
                            i = 0

                            while i < len(content):
                                char = content[i]

                                # 检查代码块标记
                                if content[i:i+3] == '```':
                                    in_code_block = not in_code_block
                                    current_chunk += content[i:i+3]
                                    i += 3
                                    continue

                                # 检查行内代码标记
                                elif char == '`' and not in_code_block:
                                    in_inline_code = not in_inline_code
                                    current_chunk += char
                                    i += 1
                                    continue

                                # 如果在代码块或行内代码中，不分割
                                if in_code_block or in_inline_code:
                                    current_chunk += char
                                    i += 1
                                    continue

                                # 普通文本的分割逻辑
                                if char in '，。！？；：':
                                    current_chunk += char
                                    if current_chunk.strip():
                                        chunks.append(current_chunk)
                                    current_chunk = ""
                                elif char == '\n':
                                    current_chunk += char
                                    if current_chunk.strip():
                                        chunks.append(current_chunk)
                                    current_chunk = ""
                                elif char == ' ' and len(current_chunk) > 20:
                                    current_chunk += char
                                    if current_chunk.strip():
                                        chunks.append(current_chunk)
                                    current_chunk = ""
                                else:
                                    current_chunk += char

                                i += 1

                            # 添加最后的块
                            if current_chunk.strip():
                                chunks.append(current_chunk)

                            return chunks

                        chunks = smart_split_content(full_response)

                        for i, chunk in enumerate(chunks):
                            response_content += chunk + '\n'
                            # 每几个块更新一次数据库
                            if i % 3 == 0:
                                assistant_message.content = response_content.strip()
                                await assistant_message.save()

                            yield {
                                "type": "content",
                                "content": chunk,
                                "message_id": assistant_message.id,
                                "conversation_id": conversation_id
                            }

                            # 根据块类型调整延迟
                            if chunk in '，。！？；：':
                                await asyncio.sleep(0.3)
                            elif '\n' in chunk:
                                await asyncio.sleep(0.4)
                            elif chunk.isspace():
                                await asyncio.sleep(0.1)
                            else:
                                await asyncio.sleep(0.2)

                        # 完成处理
                        processing_time = time.time() - start_time
                        assistant_message.status = MessageStatus.COMPLETED
                        assistant_message.processing_time = processing_time
                        assistant_message.model_name = settings.default_model

                        # 计算Token使用量
                        assistant_message.tokens_used = self._estimate_tokens(user_message, response_content)

                        await assistant_message.save()

                        yield {
                            "type": "complete",
                            "message_id": assistant_message.id,
                            "conversation_id": conversation_id,
                            "processing_time": processing_time
                        }
                        return
                except Exception as autogen_error:
                    print(f"Autogen调用失败: {autogen_error}")
            # 如果所有方法都失败，使用模拟模式
            async for chunk in self._generate_mock_response_stream(assistant_message, user_message):
                yield chunk
        except Exception as e:
            # 处理错误
            assistant_message.status = MessageStatus.FAILED
            assistant_message.content = f"处理消息时发生错误: {str(e)}"
            await assistant_message.save()

            yield {
                "type": "error",
                "error": str(e),
                "message_id": assistant_message.id,
                "conversation_id": conversation_id
            }

    async def _generate_mock_response_stream(
            self,
            assistant_message: Message,
            user_message: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成模拟响应流"""
        # 根据用户消息生成更智能的Markdown响应
        if "测试" in user_message or "软件" in user_message:
            mock_response = """# 软件测试技能学习指南

## 🎯 一、基础核心技能

### 1. 测试理论基础
无论你最终选择哪个细分方向，这些都是必须掌握的基石：

- **软件测试生命周期 (STLC)**: 了解测试在整个软件开发生命周期（SDLC）中的位置、阶段和作用。

- **测试类型**: 熟悉并理解各种测试类型，如：
  - **功能测试**: 验证软件功能是否符合需求。
  - **非功能测试**:
    - **性能测试**: 评估软件在不同负载下的响应速度、稳定性、资源消耗。
    - **安全测试**: 发现软件潜在的安全漏洞和弱点。
    - **可用性测试 (Usability Testing)**: 评估用户界面是否直观、易用。
    - **兼容性测试**: 确保软件在不同环境（操作系统、浏览器、设备）下正常运行。
    - **可靠性测试、可维护性测试** 等。

- **结构测试 (白盒测试)**: 关注代码内部结构。

```python
# 示例: Pytest测试框架结构
project/
├── fixtures/
├── testcases/
│   ├── web/
│   ├── api/
├── utils/
├── conftest.py
└── pytest.ini
```

## 🔧 二、持续集成与质量保障

### DevOps工具链
1. **版本控制**: Git + GitLab
2. **持续集成**: Jenkins Pipeline

> **重要提示**: 软件测试是确保软件质量、稳定性和用户体验的关键环节，是软件开发流程中不可或缺的一部分。

如果您需要更多帮助，请随时告诉我！"""
        elif "代码" in user_message or "编程" in user_message:
            mock_response = """# 编程学习指南

## 基础语法

### Python示例
```python
def hello_world():
    print("Hello, World!")
    return True

# 列表推导式
numbers = [x**2 for x in range(10)]
print(numbers)
```

### JavaScript示例
```javascript
const greet = (name) => {
    console.log(`Hello, ${name}!`);
};

// 异步函数
async function fetchData() {
    const response = await fetch('/api/data');
    return response.json();
}
```

## 学习路径

1. **基础语法**
2. **数据结构与算法**
3. **项目实践**
4. **开源贡献**

> 编程是一门实践性很强的技能，需要大量的练习和项目经验。

**记住**: 持续学习和实践是成为优秀程序员的关键！"""
        else:
            mock_response = f"""# AI助手回复

您好！我收到了您的消息：

> {user_message}

## 我的回答

这是一个AI助手的回复。我理解您的问题，让我来为您详细解答。

### 功能特点

- **智能对话**: 基于先进的AI模型
- **实时响应**: 支持流式输出
- **多格式支持**: Markdown渲染

```bash
# 示例命令
npm install
npm run dev
```

作为AI助手，我可以帮助您解决各种问题，包括：

1. 回答问题
2. 提供建议
3. 协助分析
4. 代码编写

**如果您需要更多帮助，请随时告诉我！**"""
        # 模拟真实的流式输出 - 智能分割，保护代码块
        response_content = ""

        # 智能分割函数，保护代码块完整性
        def smart_split_content(content: str) -> list:
            chunks = []
            current_chunk = ""
            in_code_block = False
            in_inline_code = False
            i = 0

            while i < len(content):
                char = content[i]

                # 检查代码块标记
                if content[i:i+3] == '```':
                    in_code_block = not in_code_block
                    current_chunk += content[i:i+3]
                    i += 3
                    continue

                # 检查行内代码标记
                elif char == '`' and not in_code_block:
                    in_inline_code = not in_inline_code
                    current_chunk += char
                    i += 1
                    continue

                # 如果在代码块或行内代码中，不分割
                if in_code_block or in_inline_code:
                    current_chunk += char
                    i += 1
                    continue

                # 普通文本的分割逻辑
                if char in '，。！？；：':
                    current_chunk += char
                    if current_chunk.strip():
                        chunks.append(current_chunk)
                    current_chunk = ""
                elif char == '\n':
                    current_chunk += char
                    if current_chunk.strip():
                        chunks.append(current_chunk)
                    current_chunk = ""
                elif char == ' ' and len(current_chunk) > 20:  # 长句子在空格处分割
                    current_chunk += char
                    if current_chunk.strip():
                        chunks.append(current_chunk)
                    current_chunk = ""
                else:
                    current_chunk += char

                i += 1

            # 添加最后的块
            if current_chunk.strip():
                chunks.append(current_chunk)

            return chunks

        chunks = smart_split_content(mock_response)

        for i, chunk in enumerate(chunks):
            response_content += chunk

            # 每几个块更新一次数据库（减少IO）
            if i % 3 == 0:
                assistant_message.content = response_content
                await assistant_message.save()

            # 发送流式数据
            yield {
                "type": "content",
                "content": chunk,
                "message_id": assistant_message.id,
                "conversation_id": assistant_message.conversation.id
            }

            # 模拟打字延迟 - 根据块的类型调整延迟
            if chunk in '，。！？；：':  # 标点符号稍微停顿长一点
                await asyncio.sleep(0.3)
            elif '\n' in chunk:  # 换行停顿
                await asyncio.sleep(0.4)
            elif chunk.isspace():  # 空格稍微停顿
                await asyncio.sleep(0.1)
            else:  # 普通文本块
                await asyncio.sleep(0.2)

        # 最终保存完整内容
        assistant_message.content = response_content
        assistant_message.status = MessageStatus.COMPLETED
        assistant_message.model_name = "mock-model"

        # 计算Token使用量
        assistant_message.tokens_used = self._estimate_tokens(user_message, response_content)

        await assistant_message.save()

        yield {
            "type": "complete",
            "message_id": assistant_message.id,
            "conversation_id": assistant_message.conversation.id,
            "processing_time": 2.0
        }


# 全局AI服务实例
ai_service = AIService()
