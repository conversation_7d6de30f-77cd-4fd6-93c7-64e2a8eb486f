#!/usr/bin/env python3
"""
文件上传功能测试脚本

用于测试文件上传和处理功能是否正常工作，特别是修复 "I/O operation on closed file" 错误后的验证。

使用方法:
    python test_file_upload.py

作者: jinglv
时间: 2025年1月
"""

import asyncio
import sys
import os
from pathlib import Path
from io import BytesIO

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.file_service import file_service
from models.chat import Message, Conversation, MessageRole, MessageStatus
from fastapi import UploadFile


class MockUploadFile:
    """模拟UploadFile对象用于测试"""
    
    def __init__(self, filename: str, content: bytes, content_type: str):
        self.filename = filename
        self.content = content
        self.content_type = content_type
        self.size = len(content)
        self._file = BytesIO(content)
        
    async def read(self) -> bytes:
        """读取文件内容"""
        return self.content
        
    async def seek(self, position: int):
        """移动文件指针"""
        self._file.seek(position)


async def create_test_pdf():
    """创建一个简单的测试PDF内容"""
    # 这里创建一个简单的PDF内容用于测试
    # 实际应用中，这应该是真实的PDF文件
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Hello World!) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return pdf_content


async def test_file_upload():
    """测试文件上传功能"""
    print("=" * 60)
    print("文件上传功能测试")
    print("=" * 60)
    
    try:
        # 初始化数据库（如果需要）
        from tortoise import Tortoise
        from config import TORTOISE_ORM
        
        await Tortoise.init(config=TORTOISE_ORM)
        await Tortoise.generate_schemas()
        
        # 创建测试对话和消息
        conversation = await Conversation.create(title="文件上传测试")
        message = await Message.create(
            conversation=conversation,
            role=MessageRole.USER,
            content="测试文件上传",
            status=MessageStatus.COMPLETED
        )
        
        print(f"✅ 创建测试对话: {conversation.id}")
        print(f"✅ 创建测试消息: {message.id}")
        
        # 测试1: 文本文件
        print("\n📄 测试1: 文本文件上传")
        text_content = "这是一个测试文本文件\n包含中文内容\n用于测试文件上传功能"
        text_file = MockUploadFile(
            filename="test.txt",
            content=text_content.encode('utf-8'),
            content_type="text/plain"
        )
        
        try:
            attachment = await file_service.create_file_attachment(message, text_file)
            print(f"✅ 文本文件上传成功: {attachment.filename}")
            print(f"   文件路径: {attachment.file_path}")
            print(f"   提取文本: {attachment.extracted_text[:100]}...")
        except Exception as e:
            print(f"❌ 文本文件上传失败: {e}")
        
        # 测试2: PDF文件
        print("\n📄 测试2: PDF文件上传")
        pdf_content = await create_test_pdf()
        pdf_file = MockUploadFile(
            filename="test.pdf",
            content=pdf_content,
            content_type="application/pdf"
        )
        
        try:
            attachment = await file_service.create_file_attachment(message, pdf_file)
            print(f"✅ PDF文件上传成功: {attachment.filename}")
            print(f"   文件路径: {attachment.file_path}")
            print(f"   提取文本: {attachment.extracted_text[:100]}...")
        except Exception as e:
            print(f"❌ PDF文件上传失败: {e}")
        
        # 测试3: JSON文件
        print("\n📄 测试3: JSON文件上传")
        json_content = '{"name": "测试", "type": "文件上传", "status": "成功"}'
        json_file = MockUploadFile(
            filename="test.json",
            content=json_content.encode('utf-8'),
            content_type="application/json"
        )
        
        try:
            attachment = await file_service.create_file_attachment(message, json_file)
            print(f"✅ JSON文件上传成功: {attachment.filename}")
            print(f"   文件路径: {attachment.file_path}")
            print(f"   提取文本: {attachment.extracted_text}")
        except Exception as e:
            print(f"❌ JSON文件上传失败: {e}")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理数据库连接
        await Tortoise.close_connections()


async def test_file_service_directly():
    """直接测试文件服务功能"""
    print("=" * 60)
    print("文件服务直接测试")
    print("=" * 60)
    
    # 测试文件验证
    print("📋 测试文件验证功能")
    
    # 创建测试文件
    test_file = MockUploadFile(
        filename="test.txt",
        content=b"Hello World",
        content_type="text/plain"
    )
    
    try:
        result = file_service.validate_file(test_file)
        print(f"✅ 文件验证结果: {result}")
    except Exception as e:
        print(f"❌ 文件验证失败: {e}")
    
    # 测试文件保存
    print("\n💾 测试文件保存功能")
    try:
        file_path, file_type = await file_service.save_file(test_file)
        print(f"✅ 文件保存成功: {file_path}")
        print(f"   文件类型: {file_type}")
        
        # 测试文本提取
        print("\n🔍 测试文本提取功能")
        extracted_text = await file_service.extract_text_content(file_path, file_type)
        print(f"✅ 文本提取成功: {extracted_text}")
        
        # 清理测试文件
        if Path(file_path).exists():
            Path(file_path).unlink()
            print(f"🗑️ 清理测试文件: {file_path}")
            
    except Exception as e:
        print(f"❌ 文件保存失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("文件上传功能测试脚本")
    print("用于验证修复 'I/O operation on closed file' 错误")
    
    # 测试1: 直接测试文件服务
    await test_file_service_directly()
    
    print("\n" + "=" * 80 + "\n")
    
    # 测试2: 完整的文件上传流程
    await test_file_upload()


if __name__ == "__main__":
    asyncio.run(main())
