from enum import Enum

from tortoise import fields
from tortoise.models import Model


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageStatus(str, Enum):
    """消息状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class Conversation(Model):
    """对话会话模型"""

    id = fields.IntField(pk=True)
    title = fields.CharField(max_length=200, default="新对话")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    is_active = fields.BooleanField(default=True)

    # 关联消息
    messages: fields.ReverseRelation["Message"]

    class Meta:
        table = "conversations"
        ordering = ["-updated_at"]

    def __str__(self):
        return f"Conversation({self.id}): {self.title}"


class Message(Model):
    """消息模型"""

    id = fields.IntField(pk=True)
    conversation = fields.ForeignKeyField(
        "models.Conversation",
        related_name="messages",
        on_delete=fields.CASCADE
    )
    role = fields.CharEnumField(MessageRole, max_length=20)
    content = fields.TextField()
    status = fields.CharEnumField(MessageStatus, max_length=20, default=MessageStatus.COMPLETED)

    # 元数据
    model_name = fields.CharField(max_length=100, null=True)
    tokens_used = fields.IntField(null=True)
    processing_time = fields.FloatField(null=True)  # 处理时间（秒）

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "messages"
        ordering = ["created_at"]

    def __str__(self):
        return f"Message({self.id}): {self.role} - {self.content[:50]}..."


class AgentSession(Model):
    """Agent会话模型"""

    id = fields.IntField(pk=True)
    conversation = fields.ForeignKeyField(
        "models.Conversation",
        related_name="agent_sessions",
        on_delete=fields.CASCADE
    )

    # Agent配置
    agent_name = fields.CharField(max_length=100)
    agent_config = fields.JSONField(default=dict)  # Agent配置信息

    # 会话状态
    is_active = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "agent_sessions"

    def __str__(self):
        return f"AgentSession({self.id}): {self.agent_name}"
