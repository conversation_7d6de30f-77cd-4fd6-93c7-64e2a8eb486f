# 数据库表创建指南

## 问题描述
`file_attachments` 表没有创建，需要手动创建数据库表。

## 解决方案

### 方法1：使用提供的脚本（推荐）

```bash
cd backend
python create_tables.py
```

这个脚本会：
- 检查现有表
- 创建缺失的表
- 验证创建结果
- 显示表结构信息

### 方法2：使用 Aerich 迁移工具

如果您想使用 Aerich 进行数据库迁移管理：

#### 1. 初始化 Aerich
```bash
cd backend
aerich init -t config.TORTOISE_ORM
```

#### 2. 初始化数据库
```bash
aerich init-db
```

#### 3. 生成迁移文件
```bash
aerich migrate --name add_file_attachments
```

#### 4. 应用迁移
```bash
aerich upgrade
```

### 方法3：直接使用 Python 脚本

创建一个简单的 Python 脚本：

```python
import asyncio
from tortoise import Tortoise
from config import TORTOISE_ORM

async def create_tables():
    await Tortoise.init(config=TORTOISE_ORM)
    await Tortoise.generate_schemas()
    await Tortoise.close_connections()
    print("✅ 数据库表创建完成")

if __name__ == "__main__":
    asyncio.run(create_tables())
```

### 方法4：通过应用启动自动创建

在 `main.py` 中，Tortoise ORM 已经配置了 `generate_schemas=True`：

```python
register_tortoise(
    app,
    config=TORTOISE_ORM,
    generate_schemas=True,  # 这会自动创建表
    add_exception_handlers=True,
)
```

所以理论上启动应用时应该会自动创建表。如果没有创建，可能是因为：
1. 模型没有正确导入
2. 配置有问题

## 验证表是否创建成功

### 使用 SQLite 命令行工具
```bash
cd backend
sqlite3 db.sqlite3
.tables
.schema file_attachments
.quit
```

### 使用 Python 脚本验证
```python
import sqlite3

conn = sqlite3.connect('db.sqlite3')
cursor = conn.cursor()

# 检查表是否存在
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='file_attachments'")
result = cursor.fetchone()

if result:
    print("✅ file_attachments 表已存在")
    
    # 查看表结构
    cursor.execute("PRAGMA table_info(file_attachments)")
    columns = cursor.fetchall()
    print("表结构:")
    for col in columns:
        print(f"  {col[1]}: {col[2]}")
else:
    print("❌ file_attachments 表不存在")

conn.close()
```

## 表结构说明

`file_attachments` 表应该包含以下字段：

```sql
CREATE TABLE file_attachments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    extracted_text TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
);
```

## 常见问题

### Q: 为什么表没有自动创建？
A: 可能的原因：
1. `FileAttachment` 模型没有在 `models/__init__.py` 中导入
2. 应用启动时出现错误
3. 数据库文件权限问题

### Q: 如何重新创建所有表？
A: 
1. 删除 `db.sqlite3` 文件
2. 重新启动应用或运行创建脚本

### Q: 如何查看当前数据库中的所有表？
A: 使用提供的 `create_tables.py` 脚本，它会显示所有表的信息。

## 执行命令总结

**最简单的方法**：
```bash
cd backend
python create_tables.py
```

**如果上面的方法不行**：
```bash
cd backend
python -c "
import asyncio
from tortoise import Tortoise
from config import TORTOISE_ORM

async def create():
    await Tortoise.init(config=TORTOISE_ORM)
    await Tortoise.generate_schemas()
    await Tortoise.close_connections()
    print('✅ 完成')

asyncio.run(create())
"
```

执行完成后，`file_attachments` 表应该就会被创建了。
