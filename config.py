from typing import Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""

    # 应用基础配置
    app_name: str = "AI系统"
    app_version: str = "1.0.0"
    debug: bool = True

    # 数据库配置
    database_url: str = "sqlite://db.sqlite3"

    # API配置
    api_prefix: str = "/api"
    cors_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # AI模型配置
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"

    # Autogen配置
    autogen_config_list: list = []

    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 动态构建autogen配置
        if self.openai_api_key:
            self.autogen_config_list.append({
                "models": self.default_model,
                "api_key": self.openai_api_key,
                "base_url": self.openai_base_url,
                "api_type": "openai"
            })


# 全局设置实例
settings = Settings()

# Tortoise-ORM配置
TORTOISE_ORM = {
    "connections": {"default": settings.database_url},
    "apps": {
        "models": {
            "models": ["models", "aerich.models"],
            "default_connection": "default",
        },
    },
}
