import time
from typing import AsyncGenerator, Dict, List, Optional, Any, Union

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import settings
from models.chat import Conversation, Message, MessageRole, MessageStatus


class AIService:
    """AI服务类，负责处理与Autogen的交互"""

    def __init__(self):
        self.agent: str
        self.group_chats: Dict[int, RoundRobinGroupChat] = {}
        self._initialize_agents()

    def _initialize_agents(self):
        """初始化AI Agents"""
        try:
            # 创建模型客户端
            model_clients = []

            if settings.openai_api_key:
                model_client = OpenAIChatCompletionClient(
                    model=settings.default_model,
                    api_key=settings.openai_api_key,
                    base_url=settings.openai_base_url,
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": ModelFamily.UNKNOWN,
                        "structured_output": True,
                        "multiple_system_messages": True,
                    }
                )
                model_clients.append(model_client)

            if not model_clients:
                # 如果没有配置API密钥，使用默认配置
                print("警告: 未配置AI模型API密钥，将使用模拟模式")
                return

            # 创建助手Agent
            self.agent = AssistantAgent(
                name="assistant",
                model_client=model_clients[0],
                system_message="你是一个有用的AI助手，能够回答各种问题并提供帮助。请用中文回复。",
                model_client_stream=True
            )
            print(f"✅ Autogen Agent初始化成功，使用模型: {settings.default_model}")
        except Exception as e:
            print(f"初始化AI Agents失败: {e}")

    async def create_conversation(self, title: str = "新对话") -> Conversation:
        """创建新对话"""
        conversation = await Conversation.create(title=title)
        return conversation

    async def get_conversation(self, conversation_id: int) -> Optional[Conversation]:
        """获取对话"""
        return await Conversation.get_or_none(id=conversation_id)

    async def get_conversation_messages(self, conversation_id: int) -> List[Message]:
        """获取对话消息历史"""
        return await Message.filter(conversation_id=conversation_id).order_by("created_at")

    async def send_message(
            self,
            conversation_id: int,
            content: str,
            role: MessageRole = MessageRole.USER
    ) -> Message:
        """发送消息"""
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 保存用户消息
        user_message = await Message.create(
            conversation=conversation,
            role=role,
            content=content,
            status=MessageStatus.COMPLETED
        )
        return user_message

    async def _auto_set_conversation_title(self, conversation: Conversation, user_message: str):
        """根据第一条用户消息自动设置对话标题"""
        # 检查是否是第一条用户消息（除了刚创建的消息，应该只有1条消息）
        message_count = await Message.filter(conversation=conversation).count()
        if message_count <= 1 and conversation.title == "新对话":
            # 生成简洁的标题（取前30个字符）
            title = user_message.strip()
            if len(title) > 30:
                title = title[:30] + "..."
            # 移除换行符和多余空格
            title = " ".join(title.split())
            # 更新对话标题
            conversation.title = title
            await conversation.save()
            print(f"✅ 自动设置对话标题: {title}")

    async def generate_response(
            self,
            conversation_id: int,
            user_message: str
    ) -> Optional[Dict[str, Union[str, int, float, Any]]]:
        """生成非流式响应 - 使用 agent.run 方法"""
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 保存用户消息
        await self.send_message(conversation_id, user_message, MessageRole.USER)

        # 检查是否需要自动设置对话标题
        await self._auto_set_conversation_title(conversation, user_message)

        # 创建助手消息记录
        assistant_message = await Message.create(
            conversation=conversation,
            role=MessageRole.ASSISTANT,
            content="",
            status=MessageStatus.PROCESSING
        )

        start_time = time.time()
        try:
            if self.agent:
                # 使用 agent.run 方法进行非流式调用
                result = await self.agent.run(task=user_message)

                if result:
                    response_content = str(result)
                    # 完成处理
                    processing_time = time.time() - start_time
                    assistant_message.content = response_content
                    assistant_message.status = MessageStatus.COMPLETED
                    assistant_message.processing_time = processing_time
                    assistant_message.model_name = settings.default_model
                    if isinstance(result, TextMessage) and result.models_usage is not None:
                        # 获取花费的token
                        assistant_message.tokens_used = result.models_usage.completion_tokens
                    # 保存数据库
                    await assistant_message.save()

                    return {
                        "message_id": assistant_message.id,
                        "conversation_id": conversation_id,
                        "content": response_content,
                        "processing_time": processing_time,
                        "status": "completed"
                    }
        except Exception as autogen_error:
            print(f"Autogen非流式调用失败: {autogen_error}")

    async def generate_response_stream(
            self,
            conversation_id: int,
            user_message: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成流式响应"""
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")
        # 保存用户消息
        await self.send_message(conversation_id, user_message, MessageRole.USER)

        # 检查是否需要自动设置对话标题
        await self._auto_set_conversation_title(conversation, user_message)

        # 创建助手消息记录
        assistant_message = await Message.create(
            conversation=conversation,
            role=MessageRole.ASSISTANT,
            content="",
            status=MessageStatus.PROCESSING
        )
        start_time = time.time()
        try:
            # 获取消息历史
            messages = await self.get_conversation_messages(conversation_id)
            # 构建对话历史
            chat_history = []
            for msg in messages[:-1]:  # 排除刚创建的assistant_message
                if msg.role != MessageRole.SYSTEM:
                    chat_history.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })
            # 添加当前用户消息
            chat_history.append({
                "role": "user",
                "content": user_message
            })
            response_content = ""
            # 尝试使用Autogen的真正流式输出
            if self.agent:
                try:
                    # 使用 agent.run_stream 方法进行真正的流式调用
                    stream_response = self.agent.run_stream(task=user_message)
                    async for chunk in stream_response:
                        if isinstance(chunk, ModelClientStreamingChunkEvent):
                            # 处理流式内容块
                            content = chunk.content
                            if content:
                                response_content += content
                                # 更新数据库
                                assistant_message.content = response_content
                                # await assistant_message.save()

                                yield {
                                    "type": "content",
                                    "content": content,
                                    "message_id": assistant_message.id,
                                    "conversation_id": conversation_id
                                }
                        elif isinstance(chunk, TextMessage) and chunk.models_usage is not None:
                            # 获取花费的token
                            assistant_message.tokens_used = chunk.models_usage.completion_tokens
                        # 完成处理
                    processing_time = time.time() - start_time
                    assistant_message.status = MessageStatus.COMPLETED
                    assistant_message.processing_time = processing_time
                    assistant_message.model_name = settings.default_model
                    await assistant_message.save()

                    yield {
                        "type": "complete",
                        "message_id": assistant_message.id,
                        "conversation_id": conversation_id,
                        "processing_time": processing_time
                    }
                    return
                except Exception as autogen_error:
                    print(f"Autogen调用失败: {autogen_error}")
        except Exception as e:
            # 处理错误
            assistant_message.status = MessageStatus.FAILED
            assistant_message.content = f"处理消息时发生错误: {str(e)}"
            await assistant_message.save()

            yield {
                "type": "error",
                "error": str(e),
                "message_id": assistant_message.id,
                "conversation_id": conversation_id
            }


# 全局AI服务实例
ai_service = AIService()
