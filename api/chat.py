import json
from typing import List, Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from models.chat import Conversation, MessageRole
from services.ai_service import ai_service

router = APIRouter(prefix="/chat", tags=["聊天"])


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    conversation_id: Optional[int] = None


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message_id: int
    conversation_id: int
    content: str
    role: str
    created_at: str


class ConversationResponse(BaseModel):
    """对话响应模型"""
    id: int
    title: str
    created_at: str
    updated_at: str
    message_count: int


class MessageResponse(BaseModel):
    """消息响应模型"""
    id: int
    role: str
    content: str
    status: str
    created_at: str
    model_name: Optional[str] = None
    processing_time: Optional[float] = None
    tokens_used: Optional[int] = None


@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(title: str = "新对话"):
    """创建新对话"""
    try:
        conversation = await ai_service.create_conversation(title)
        return ConversationResponse(
            id=conversation.id,
            title=conversation.title,
            created_at=conversation.created_at.isoformat(),
            updated_at=conversation.updated_at.isoformat(),
            message_count=0
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")


@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations():
    """获取对话列表"""
    try:
        conversations = await Conversation.all().prefetch_related("messages")
        return [
            ConversationResponse(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=len(conv.messages)
            )
            for conv in conversations
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")


@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(conversation_id: int):
    """获取对话消息"""
    try:
        messages = await ai_service.get_conversation_messages(conversation_id)
        return [
            MessageResponse(
                id=msg.id,
                role=msg.role.value,
                content=msg.content,
                status=msg.status.value,
                created_at=msg.created_at.isoformat(),
                model_name=msg.model_name,
                processing_time=msg.processing_time,
                tokens_used=msg.tokens_used
            )
            for msg in messages
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取消息失败: {str(e)}")


@router.post("/send")
async def send_message(request: ChatRequest):
    """发送聊天消息（非流式）- 使用 agent.run 方法"""
    try:
        # 如果没有指定对话ID，创建新对话
        if request.conversation_id is None:
            conversation = await ai_service.create_conversation()
            conversation_id = conversation.id
        else:
            conversation_id = request.conversation_id

        # 发送用户消息
        user_message = await ai_service.send_message(
            conversation_id,
            request.message,
            MessageRole.USER
        )

        # 使用非流式方法生成响应
        result = await ai_service.generate_response(conversation_id, request.message)

        return {
            "conversation_id": conversation_id,
            "message_id": result["message_id"],
            "content": result["content"],
            "processing_time": result["processing_time"],
            "status": result["status"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.post("/send-stream")
async def send_message_stream_fallback(request: ChatRequest):
    """发送聊天消息（使用流式但收集完整响应）- 兼容旧版本"""
    try:
        # 如果没有指定对话ID，创建新对话
        if request.conversation_id is None:
            conversation = await ai_service.create_conversation()
            conversation_id = conversation.id
        else:
            conversation_id = request.conversation_id

        # 发送用户消息
        user_message = await ai_service.send_message(
            conversation_id,
            request.message,
            MessageRole.USER
        )

        # 生成AI响应（流式但收集完整内容）
        response_content = ""
        async for chunk in ai_service.generate_response_stream(conversation_id, request.message):
            if chunk["type"] == "content":
                response_content += chunk["content"]
            elif chunk["type"] == "complete":
                break

        return {
            "conversation_id": conversation_id,
            "user_message_id": user_message.id,
            "response": response_content.strip()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.get("/stream/{conversation_id}")
async def chat_stream(conversation_id: int, message: str):
    """SSE流式聊天接口 - 使用 agent.run_stream 方法"""

    async def generate_stream():
        try:
            async for chunk in ai_service.generate_response_stream(conversation_id, message):
                # 格式化SSE数据
                data = json.dumps(chunk, ensure_ascii=False)
                yield f"data: {data}\n\n"

                # 如果是完成或错误，结束流
                if chunk["type"] in ["complete", "error"]:
                    break

        except Exception as e:
            error_data = json.dumps({
                "type": "error",
                "error": str(e)
            }, ensure_ascii=False)
            yield f"data: {error_data}\n\n"

        # 发送结束信号
        yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: int):
    """删除对话"""
    try:
        conversation = await ai_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        await conversation.delete()
        return {"message": "对话删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "chat-api",
        "agent_loaded": bool(ai_service.agent)
    }
