#!/usr/bin/env python3
"""
API接口测试脚本

用于直接测试 /api/chat/conversations 接口，帮助诊断 500 错误。

使用方法:
    python test_api_endpoint.py

作者: jinglv
时间: 2025年1月
"""

import asyncio
import sys
import os
import traceback
from fastapi.testclient import TestClient

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主应用
from main import app


def test_conversations_endpoint():
    """测试对话列表接口"""
    print("=" * 60)
    print("测试 /api/chat/conversations 接口")
    print("=" * 60)
    
    try:
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试健康检查接口
        print("🔄 测试健康检查接口...")
        health_response = client.get("/api/chat/health")
        print(f"健康检查状态码: {health_response.status_code}")
        if health_response.status_code == 200:
            print(f"健康检查响应: {health_response.json()}")
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {health_response.text}")
            return False
        
        # 测试创建对话接口
        print("\n🔄 测试创建对话接口...")
        create_response = client.post("/api/chat/conversations?title=测试对话")
        print(f"创建对话状态码: {create_response.status_code}")
        if create_response.status_code == 200:
            print(f"创建对话响应: {create_response.json()}")
            print("✅ 创建对话成功")
        else:
            print(f"❌ 创建对话失败: {create_response.text}")
        
        # 测试获取对话列表接口
        print("\n🔄 测试获取对话列表接口...")
        list_response = client.get("/api/chat/conversations")
        print(f"获取对话列表状态码: {list_response.status_code}")
        
        if list_response.status_code == 200:
            conversations = list_response.json()
            print(f"✅ 获取对话列表成功，共 {len(conversations)} 个对话")
            
            for i, conv in enumerate(conversations):
                print(f"  {i+1}. ID={conv.get('id')}, 标题='{conv.get('title')}', 消息数={conv.get('message_count')}")
            
            return True
        else:
            print(f"❌ 获取对话列表失败")
            print(f"状态码: {list_response.status_code}")
            print(f"响应内容: {list_response.text}")
            
            # 尝试解析错误信息
            try:
                error_detail = list_response.json()
                print(f"错误详情: {error_detail}")
            except:
                print("无法解析错误响应为JSON")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False


def test_other_endpoints():
    """测试其他相关接口"""
    print("\n" + "=" * 60)
    print("测试其他相关接口")
    print("=" * 60)
    
    try:
        client = TestClient(app)
        
        # 测试根路径
        print("🔄 测试根路径...")
        root_response = client.get("/")
        print(f"根路径状态码: {root_response.status_code}")
        if root_response.status_code == 200:
            print("✅ 根路径正常")
        else:
            print(f"❌ 根路径异常: {root_response.text}")
        
        # 测试API文档
        print("\n🔄 测试API文档...")
        docs_response = client.get("/docs")
        print(f"API文档状态码: {docs_response.status_code}")
        if docs_response.status_code == 200:
            print("✅ API文档正常")
        else:
            print(f"❌ API文档异常: {docs_response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试其他接口时发生错误: {e}")
        traceback.print_exc()
        return False


async def test_database_directly():
    """直接测试数据库操作"""
    print("\n" + "=" * 60)
    print("直接测试数据库操作")
    print("=" * 60)
    
    try:
        from tortoise import Tortoise
        from config import TORTOISE_ORM
        from models.chat import Conversation
        
        # 初始化数据库
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 确保表存在
        await Tortoise.generate_schemas()
        
        # 测试查询操作
        print("🔄 测试数据库查询...")
        conversations = await Conversation.all().prefetch_related("messages")
        print(f"✅ 数据库查询成功，共 {len(conversations)} 个对话")
        
        # 如果没有数据，创建一些测试数据
        if len(conversations) == 0:
            print("🔄 创建测试数据...")
            test_conv = await Conversation.create(title="API测试对话")
            print(f"✅ 创建测试对话: ID={test_conv.id}")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        traceback.print_exc()
        await Tortoise.close_connections()
        return False


def main():
    """主函数"""
    print("API接口测试脚本")
    print("用于诊断 /api/chat/conversations 接口的 500 错误")
    
    # 1. 先测试数据库
    print("\n📋 第一步：测试数据库操作")
    db_result = asyncio.run(test_database_directly())
    
    if not db_result:
        print("\n❌ 数据库测试失败，请先解决数据库问题")
        return
    
    # 2. 测试其他基础接口
    print("\n📋 第二步：测试基础接口")
    basic_result = test_other_endpoints()
    
    if not basic_result:
        print("\n❌ 基础接口测试失败")
        return
    
    # 3. 测试目标接口
    print("\n📋 第三步：测试目标接口")
    target_result = test_conversations_endpoint()
    
    if target_result:
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！")
        print("/api/chat/conversations 接口应该正常工作了")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 接口测试失败")
        print("请检查上面的错误信息进行调试")
        print("=" * 60)


if __name__ == "__main__":
    main()
